<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FaciShare 导航</title>

    <!-- 外部样式依赖 -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <!-- 本地样式 -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/themes.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body data-theme="ivory-light">
    <!-- 顶部导航栏 -->
    <header class="navbar-custom">
        <div class="navbar-brand-custom">
            <img src="assets/icons/faciLogo.svg" alt="FaciShare Logo" class="logo">
            <span class="brand-text">FaciShare 导航</span>
        </div>
        
        <div class="search-container">
            <div class="search-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="searchInput" class="search-input" placeholder="搜索网站名称、描述、标签...">
                <div class="search-actions">
                    <button id="searchFilterBtn" class="search-filter-btn" title="搜索筛选">
                        <i class="fas fa-filter"></i>
                        <span class="search-filter-indicator"></span>
                    </button>
                    <span class="search-shortcut" id="searchShortcut"></span>
                </div>
            </div>
            <div id="searchResults" class="search-results"></div>
            <div id="searchFilters" class="search-filters" style="display: none;">
                <div class="search-filters-header">
                    <span class="search-filters-title">搜索筛选</span>
                    <button id="clearFilters" class="clear-filters-btn">清除筛选</button>
                </div>
                <div class="search-filters-content">
                    <div class="filter-group">
                        <label class="filter-label">分类筛选</label>
                        <div id="categoryFilter" class="custom-select">
                            <div class="custom-select-trigger">
                                <span class="custom-select-text">所有分类</span>
                                <i class="fas fa-chevron-down custom-select-arrow"></i>
                            </div>
                            <div class="custom-select-options">
                                <div class="custom-select-option active" data-value="">所有分类</div>
                                <!-- 动态生成分类选项 -->
                            </div>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label class="filter-label">标签筛选</label>
                        <div id="tagFilters" class="tag-filters">
                            <!-- 动态生成标签筛选 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="navbar-actions">
            <!-- 数据管理按钮 -->
            <div class="data-management-dropdown">
                <button class="action-btn" id="dataManageBtn" title="数据管理">
                    <i class="fas fa-database"></i>
                    <span class="d-none d-lg-inline">数据</span>
                </button>
                <div class="dropdown-menu" id="dataManageMenu">
                    <button class="dropdown-item" onclick="navApp.visitManager.exportData()">
                        <i class="fas fa-download"></i> 导出数据
                    </button>
                    <label for="importFile" class="dropdown-item">
                        <i class="fas fa-upload"></i> 导入数据
                        <input type="file" id="importFile" accept=".json" style="display:none">
                    </label>
                    <button class="dropdown-item" onclick="navApp.visitManager.recoverFromIndexedDB()">
                        <i class="fas fa-undo"></i> 恢复备份
                    </button>
                    <div class="dropdown-divider"></div>
                    <button class="dropdown-item text-danger" onclick="navApp.visitManager.clearHistory()">
                        <i class="fas fa-trash"></i> 清除历史
                    </button>
                </div>
            </div>
            <!-- 视图切换按钮 -->
            <button id="viewToggleBtn" class="action-btn view-toggle-btn" title="切换视图模式">
                <i class="fas fa-th-large" id="viewToggleIcon"></i>
                <span class="view-mode-text d-none d-lg-inline">标准</span>
            </button>

            <div class="theme-selector-dropdown" id="themeSelector">
                <button class="action-btn theme-selector-btn" id="themeSelectorBtn" title="选择主题">
                    <i class="fas fa-palette"></i>
                    <span class="theme-name d-none d-lg-inline">主题</span>
                    <i class="fas fa-chevron-down theme-dropdown-arrow"></i>
                </button>
                <div class="theme-dropdown-menu" id="themeDropdownMenu">
                    <div class="theme-option" data-theme="ivory-light">
                        <i class="fas fa-sun theme-option-icon"></i>
                        <div class="theme-option-info">
                            <span class="theme-option-name">日光象牙白</span>
                            <span class="theme-option-desc">温暖舒适的浅色主题</span>
                        </div>
                    </div>
                    <div class="theme-option" data-theme="dark-obsidian">
                        <i class="fas fa-moon theme-option-icon"></i>
                        <div class="theme-option-info">
                            <span class="theme-option-name">夜月玄玉黑</span>
                            <span class="theme-option-desc">深邃优雅的深色主题</span>
                        </div>
                    </div>
                    <div class="theme-option" data-theme="jasmine-green">
                        <i class="fas fa-leaf theme-option-icon"></i>
                        <div class="theme-option-info">
                            <span class="theme-option-name">清雅茉莉绿</span>
                            <span class="theme-option-desc">茉莉花香·禅意美学·护眼悦目</span>
                        </div>
                    </div>
                    <div class="theme-option" data-theme="navy-blue">
                        <i class="fas fa-anchor theme-option-icon"></i>
                        <div class="theme-option-info">
                            <span class="theme-option-name">深邃海军蓝</span>
                            <span class="theme-option-desc">沉稳专业的蓝色主题</span>
                        </div>
                    </div>
                    <div class="theme-option" data-theme="star-moonlight-blue">
                        <i class="fas fa-star theme-option-icon"></i>
                        <div class="theme-option-info">
                            <span class="theme-option-name">星月流光蓝</span>
                            <span class="theme-option-desc">浩瀚星空·流光溢彩·月华如水</span>
                        </div>
                    </div>
                </div>
            </div>
            <button class="action-btn help-btn" title="帮助">
                <i class="fas fa-question-circle"></i>
            </button>
            <button id="mobileMenuToggle" class="action-btn mobile-menu-btn d-md-none" title="菜单">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </header>

    <!-- 主体内容 -->
    <div class="main-container">
        <!-- 左侧边栏 -->
        <aside id="sidebar" class="sidebar">
            <nav class="category-nav">
                <ul id="categoryList" class="category-list">
                    <!-- 动态生成分类导航 -->
                </ul>
            </nav>
        </aside>

        <!-- 右侧内容区 -->
        <main class="content-area">
            <div class="content-header">
                <h2 id="currentCategoryTitle">常用推荐</h2>
                <div class="content-info">
                    <span id="sitesCount">0</span> 个网站
                </div>
            </div>
            <div id="sitesContainer" class="sites-container">
                <!-- 动态生成网站卡片 -->
            </div>
            
            <!-- 加载状态 -->
            <div id="loadingSpinner" class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-search fa-3x text-muted"></i>
                <h3>没有找到相关网站</h3>
                <p class="text-muted">尝试调整搜索关键词或选择其他分类</p>
            </div>
        </main>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div id="sidebarOverlay" class="sidebar-overlay"></div>

    <!-- Markdown 内容模态框 -->
    <div id="markdownModal" class="markdown-modal" style="display: none;">
        <div class="markdown-modal-backdrop" id="markdownModalBackdrop"></div>
        <div class="markdown-modal-content">
            <div class="markdown-modal-header">
                <h2 id="markdownModalTitle" class="markdown-modal-title">文档</h2>
                <button id="markdownModalClose" class="markdown-modal-close" title="关闭">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="markdown-modal-body">
                <div id="markdownContent" class="markdown-content">
                    <!-- Markdown 内容将在这里渲染 -->
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/platform.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/theme.js"></script>
    <script src="js/search.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/markdown.js"></script>
    <script src="js/visit-manager.js"></script>
    <script src="js/time-notification.js"></script>
    <script src="js/app.js"></script>
    <!-- 星月流光蓝主题特效 -->
    <script src="js/star-theme-effects.js"></script>
    <!-- Markdown 渲染器 -->
    <script src="js/marked.min.js"></script>
</body>
</html>